"""
ReAct Agent 改进版本 - 解决时间认知问题
添加了当前时间工具，让Agent能够正确理解"最新"和"当前"的含义
"""

import os
from dotenv import load_dotenv
from datetime import datetime

load_dotenv()

# 从环境变量获取配置信息
API_KEY = os.getenv("OPENAI_API_KEY", "sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56")
BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.ecovai.cn/v1")
MODEL = os.getenv("MODEL_NAME", "DeepSeek-V3-0324")
SEARCH1API_KEY = os.getenv("SEARCH1API_KEY", "B8676CDF-DB51-4D78-8AC2-BE11ED21FD9A")
SEARCH1API_URL = os.getenv("SEARCH1API_URL", "https://api.search1api.com/search")

print("=" * 60)
print("🤖 ReAct Agent - 时间认知改进版")
print("=" * 60)
print(f"使用模型: {MODEL}")
print(f"当前时间: {datetime.now().strftime('%Y年%m月%d日')}")
print("=" * 60)

# 导入LangChain相关模块
from langchain import hub
from langchain_openai import ChatOpenAI
from langchain_core.tools import Tool
from langchain.agents import create_react_agent, AgentExecutor
import requests

# 获取ReAct提示模板
prompt = hub.pull("hwchase17/react")

# 创建语言模型
llm = ChatOpenAI(
    model=MODEL,
    openai_api_key=API_KEY,
    openai_api_base=BASE_URL,
    temperature=0.1,
)

# 工具1: 获取当前时间
def get_current_time(input_text=""):
    """获取当前时间信息"""
    now = datetime.now()
    return f"""当前时间信息:
- 日期: {now.strftime('%Y年%m月%d日')}
- 年份: {now.year}
- 月份: {now.month}
- 提示: 搜索'最新'信息时，应该使用{now.year}年或{now.year-1}-{now.year}年的时间范围"""

# 工具2: 搜索功能
def search1api_search(query):
    """使用Search1API进行搜索"""
    try:
        print(f"🔍 正在搜索: {query}")
        
        payload = {
            "query": query,
            "search_service": "google",
            "max_results": 5,
            "crawl_results": 0,
            "image": False,
            "include_sites": [],
            "exclude_sites": [],
            "language": "zh",
            "time_range": "year"
        }
        
        headers = {
            "Authorization": f"Bearer {SEARCH1API_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(SEARCH1API_URL, json=payload, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            results = data.get("results", [])
            
            if results:
                formatted_results = []
                for i, result in enumerate(results[:3], 1):
                    title = result.get("title", "无标题")
                    snippet = result.get("snippet", "无摘要")
                    link = result.get("link", "")
                    
                    formatted_results.append(f"{i}. {title}\n   摘要: {snippet}\n   链接: {link}")
                
                search_summary = "\n\n".join(formatted_results)
                print(f"✅ 搜索完成，找到 {len(results)} 个结果")
                return f"搜索结果:\n\n{search_summary}"
            else:
                return "未找到相关搜索结果"
        else:
            return f"搜索失败: HTTP {response.status_code}"
            
    except Exception as e:
        return f"搜索错误: {str(e)}"

# 创建工具列表
tools = [
    Tool(
        name="GetCurrentTime",
        func=get_current_time,
        description="获取当前的日期和年份信息。当用户询问'最新'、'当前'、'现在'的信息时，必须先使用此工具确定正确的时间范围。"
    ),
    Tool(
        name="Search",
        func=search1api_search,
        description="搜索最新信息和知识。使用前应该先获取当前时间，确保搜索查询包含正确的年份。"
    ),
]

# 创建ReAct代理
agent = create_react_agent(llm, tools, prompt)

# 创建代理执行器
agent_executor = AgentExecutor(
    agent=agent,
    tools=tools,
    verbose=True,
    handle_parsing_errors=True,
    max_iterations=5,  # 增加迭代次数，因为现在有两个工具
)

# 测试改进后的代理
print("\n🚀 开始测试改进后的ReAct代理")
print("=" * 60)

test_questions = [
    "当前Agent最新研究进展是什么?",
    "最新的AI技术发展趋势有哪些?",
]

for i, question in enumerate(test_questions, 1):
    print(f"\n📝 测试问题 {i}: {question}")
    print("-" * 40)
    
    try:
        result = agent_executor.invoke({"input": question})
        print(f"\n✅ 测试 {i} 完成")
        
    except Exception as e:
        print(f"❌ 测试 {i} 失败: {e}")
    
    print("-" * 40)

print("\n🎉 测试完成！")
print("\n💡 改进说明:")
print("1. 添加了GetCurrentTime工具，让Agent知道当前时间")
print("2. Agent现在会先获取时间，再进行搜索")
print("3. 搜索查询会包含正确的年份(2025年)")
print("4. 解决了时间认知滞后的问题")
