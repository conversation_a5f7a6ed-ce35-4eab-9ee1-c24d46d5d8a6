"""
ReAct Agent 改进版本
使用DeepSeek模型的智能代理，具备推理和行动能力
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def setup_environment():
    """设置环境配置"""
    # 从环境变量获取配置信息，如果没有则使用默认值
    api_key = os.getenv("OPENAI_API_KEY", "sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56")
    base_url = os.getenv("OPENAI_BASE_URL", "https://api.ecovai.cn/v1")
    model_name = os.getenv("MODEL_NAME", "DeepSeek-V3-0324")
    
    print("=" * 50)
    print("🤖 ReAct Agent 配置信息")
    print("=" * 50)
    print(f"使用模型: {model_name}")
    print(f"API基础URL: {base_url}")
    print(f"API密钥: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else api_key}")
    print("=" * 50)
    
    return api_key, base_url, model_name

def create_llm(api_key, base_url, model_name):
    """创建语言模型实例"""
    try:
        from langchain_openai import ChatOpenAI
        
        llm = ChatOpenAI(
            model=model_name,
            openai_api_key=api_key,
            openai_api_base=base_url,
            temperature=0.1,  # 设置较低的温度以获得更稳定的输出
            max_tokens=2000,  # 限制输出长度
            timeout=30,  # 设置超时时间
        )
        
        print("✅ 语言模型创建成功")
        return llm
        
    except Exception as e:
        print(f"❌ 创建语言模型失败: {e}")
        sys.exit(1)

def setup_tools():
    """设置工具"""
    try:
        from langchain_community.utilities import SerpAPIWrapper
        from langchain.agents.tools import Tool
        
        # 检查是否有SerpAPI密钥
        serpapi_key = os.getenv("SERPAPI_API_KEY")
        if not serpapi_key:
            print("⚠️  警告: 未找到SERPAPI_API_KEY，搜索功能可能无法正常工作")
            print("   请在.env文件中添加您的SerpAPI密钥")
        
        # 实例化SerpAPIWrapper
        search = SerpAPIWrapper()
        
        # 准备工具列表
        tools = [
            Tool(
                name="Search",
                func=search.run,
                description="当大模型没有相关知识时，用于搜索最新信息和知识。输入应该是搜索查询。"
            ),
        ]
        
        print("✅ 工具设置成功")
        return tools
        
    except Exception as e:
        print(f"❌ 工具设置失败: {e}")
        # 如果搜索工具失败，返回空工具列表
        print("⚠️  将在没有搜索工具的情况下运行")
        return []

def create_agent(llm, tools):
    """创建ReAct代理"""
    try:
        from langchain import hub
        from langchain.agents import create_react_agent, AgentExecutor
        
        # 从hub中获取React的Prompt
        print("📥 正在获取ReAct提示模板...")
        prompt = hub.pull("hwchase17/react")
        print("✅ ReAct提示模板获取成功")
        
        # 构建ReAct代理
        agent = create_react_agent(llm, tools, prompt)
        
        # 创建代理执行器
        agent_executor = AgentExecutor(
            agent=agent, 
            tools=tools, 
            verbose=True,
            max_iterations=5,  # 限制最大迭代次数
            handle_parsing_errors=True,  # 处理解析错误
        )
        
        print("✅ ReAct代理创建成功")
        return agent_executor
        
    except Exception as e:
        print(f"❌ 创建代理失败: {e}")
        sys.exit(1)

def run_agent_demo(agent_executor):
    """运行代理演示"""
    print("\n" + "=" * 50)
    print("🚀 开始运行ReAct代理演示")
    print("=" * 50)
    
    # 测试问题列表
    test_questions = [
        "当前Agent最新研究进展是什么?",
        "什么是ReAct框架？它的主要特点是什么？",
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n📝 第{i}次测试问题: {question}")
        print("-" * 50)
        
        try:
            result = agent_executor.invoke({"input": question})
            print(f"\n✅ 第{i}次测试完成")
            print(f"🎯 最终答案: {result.get('output', '未获得输出')}")
            
        except Exception as e:
            print(f"❌ 第{i}次测试失败: {e}")
        
        print("-" * 50)

def main():
    """主函数"""
    try:
        # 1. 设置环境
        api_key, base_url, model_name = setup_environment()
        
        # 2. 创建语言模型
        llm = create_llm(api_key, base_url, model_name)
        
        # 3. 设置工具
        tools = setup_tools()
        
        # 4. 创建代理
        agent_executor = create_agent(llm, tools)
        
        # 5. 运行演示
        run_agent_demo(agent_executor)
        
        print("\n🎉 ReAct代理演示完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断程序")
    except Exception as e:
        print(f"\n💥 程序运行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
