"""
ReAct程序详细执行追踪
展示每一步的具体数据流转和内部处理过程
"""

import os
from dotenv import load_dotenv
load_dotenv()

print("🔍 ReAct程序详细执行追踪")
print("=" * 80)

# 配置
API_KEY = os.getenv("OPENAI_API_KEY", "sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56")
BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.ecovai.cn/v1")
MODEL = os.getenv("MODEL_NAME", "DeepSeek-V3-0324")

# 导入模块
from langchain import hub
from langchain_openai import ChatOpenAI
from langchain_core.tools import Tool
from langchain.agents import create_react_agent, AgentExecutor

# 创建一个带追踪的搜索工具
def traced_search(query):
    """带追踪的搜索工具"""
    print(f"\n🔧 [工具执行] Search工具被调用")
    print(f"   📥 输入参数: '{query}'")
    print(f"   ⚙️  正在处理搜索请求...")
    
    # 模拟搜索结果
    result = f"""搜索结果：关于'{query}'的最新信息：
1. AI Agent技术在2024-2025年快速发展
2. 多模态Agent成为研究热点
3. 自主决策能力显著提升
4. 在多个行业实现商业化应用"""
    
    print(f"   📤 返回结果: {result[:50]}...")
    return result

# 设置组件
prompt = hub.pull("hwchase17/react")
llm = ChatOpenAI(model=MODEL, openai_api_key=API_KEY, openai_api_base=BASE_URL, temperature=0.1)
tools = [Tool(name="Search", func=traced_search, description="搜索最新信息")]

# 创建代理
agent = create_react_agent(llm, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True, handle_parsing_errors=True, max_iterations=3)

print("\n🎯 开始执行用户查询")
print("=" * 80)

user_input = "当前Agent最新研究进展是什么?"
print(f"👤 用户输入: {user_input}")

print("\n📋 第1阶段：提示模板构建")
print("-" * 50)

# 显示提示模板的构建过程
print("🔄 模板变量填充:")
print(f"   - tools: {tools[0].name}: {tools[0].description}")
print(f"   - tool_names: {[tool.name for tool in tools]}")
print(f"   - input: {user_input}")
print(f"   - agent_scratchpad: (初始为空)")

print("\n📋 第2阶段：首次LLM调用")
print("-" * 50)

# 构建完整的初始提示
initial_prompt = prompt.format(
    tools=f"{tools[0].name}: {tools[0].description}",
    tool_names=tools[0].name,
    input=user_input,
    agent_scratchpad=""
)

print("📤 发送给LLM的完整提示:")
print("```")
print(initial_prompt)
print("```")

print("\n📋 第3阶段：模拟LLM响应")
print("-" * 50)

# 模拟LLM的第一次响应
llm_response_1 = """为了回答关于当前Agent最新研究进展的问题，我需要搜索最新的研究信息。

Action: Search
Action Input: Agent最新研究进展 2023"""

print("📥 LLM第一次响应:")
print("```")
print(llm_response_1)
print("```")

print("\n📋 第4阶段：输出解析")
print("-" * 50)

print("🔍 AgentExecutor解析LLM输出:")
print("   1. 识别到 'Action: Search'")
print("   2. 提取 Action Input: 'Agent最新研究进展 2023'")
print("   3. 准备调用Search工具")

print("\n📋 第5阶段：工具执行")
print("-" * 50)

# 模拟工具执行
search_result = traced_search("Agent最新研究进展 2023")

print("\n📋 第6阶段：构建下一轮提示")
print("-" * 50)

# 构建包含观察结果的提示
agent_scratchpad = f"""为了回答关于当前Agent最新研究进展的问题，我需要搜索最新的研究信息。

Action: Search
Action Input: Agent最新研究进展 2023
Observation: {search_result}"""

next_prompt = prompt.format(
    tools=f"{tools[0].name}: {tools[0].description}",
    tool_names=tools[0].name,
    input=user_input,
    agent_scratchpad=agent_scratchpad
)

print("🔄 更新后的agent_scratchpad:")
print("```")
print(agent_scratchpad)
print("```")

print("\n📋 第7阶段：第二次LLM调用")
print("-" * 50)

# 模拟LLM的第二次响应
llm_response_2 = """基于搜索结果，我现在可以总结当前Agent的最新研究进展。

Thought: I now know the final answer
Final Answer: 根据最新信息，当前Agent研究的主要进展包括：
1. AI Agent技术在2024-2025年快速发展
2. 多模态Agent成为研究热点，能够处理文本、图像、音频等多种输入
3. 自主决策能力显著提升，Agent能够独立完成复杂任务
4. 在医疗、金融、教育等多个行业实现商业化应用
这些进展标志着Agent技术从实验室走向实际应用的重要转折点。"""

print("📥 LLM第二次响应:")
print("```")
print(llm_response_2)
print("```")

print("\n📋 第8阶段：最终结果提取")
print("-" * 50)

print("🔍 AgentExecutor解析最终输出:")
print("   1. 识别到 'Final Answer:'")
print("   2. 提取最终答案内容")
print("   3. 结束执行循环")

print("\n🎯 执行完成")
print("=" * 80)

print("\n📊 执行统计:")
print(f"   - 总迭代次数: 2")
print(f"   - 工具调用次数: 1")
print(f"   - LLM调用次数: 2")
print(f"   - 执行状态: 成功")

print("\n💡 关键执行要点:")
print("1. 提示模板动态填充：每次迭代都会更新agent_scratchpad")
print("2. 输出解析：AgentExecutor负责解析Action和Final Answer")
print("3. 工具调用：根据Action自动调用相应的工具函数")
print("4. 状态管理：维护完整的对话历史和推理过程")
print("5. 错误处理：handle_parsing_errors确保程序稳定运行")

print("\n🔄 数据流转路径:")
print("用户输入 → 提示模板 → LLM → 输出解析 → 工具调用 → 结果观察 → 提示模板更新 → LLM → 最终答案")
