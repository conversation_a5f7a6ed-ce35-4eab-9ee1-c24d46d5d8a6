"""
分析两次agent_executor.invoke调用的区别
详细对比输出差异的原因
"""

import os
from dotenv import load_dotenv
load_dotenv()

# 配置
API_KEY = os.getenv("OPENAI_API_KEY", "sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56")
BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.ecovai.cn/v1")
MODEL = os.getenv("MODEL_NAME", "DeepSeek-V3-0324")
SEARCH1API_KEY = os.getenv("SEARCH1API_KEY", "B8676CDF-DB51-4D78-8AC2-BE11ED21FD9A")
SEARCH1API_URL = os.getenv("SEARCH1API_URL", "https://api.search1api.com/search")

print("🔍 分析两次agent_executor调用的区别")
print("=" * 80)

# 导入必要模块
from langchain import hub
from langchain_openai import ChatOpenAI
from langchain_core.tools import Tool
from langchain.agents import create_react_agent, AgentExecutor
import requests
import time

# 创建带计数器的搜索工具
call_counter = 0

def tracked_search(query):
    """带追踪的搜索工具"""
    global call_counter
    call_counter += 1
    
    print(f"\n🔧 [第{call_counter}次工具调用] Search工具被调用")
    print(f"   📥 查询内容: '{query}'")
    print(f"   🕐 调用时间: {time.strftime('%H:%M:%S')}")
    
    try:
        payload = {
            "query": query,
            "search_service": "google",
            "max_results": 5,
            "crawl_results": 0,
            "image": False,
            "include_sites": [],
            "exclude_sites": [],
            "language": "zh",
            "time_range": "year"
        }
        
        headers = {
            "Authorization": f"Bearer {SEARCH1API_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(SEARCH1API_URL, json=payload, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            results = data.get("results", [])
            
            if results:
                formatted_results = []
                for i, result in enumerate(results[:3], 1):
                    title = result.get("title", "无标题")
                    snippet = result.get("snippet", "无摘要")
                    link = result.get("link", "")
                    
                    formatted_results.append(f"{i}. {title}\n   摘要: {snippet}\n   链接: {link}")
                
                search_summary = "\n\n".join(formatted_results)
                print(f"   ✅ 返回 {len(results)} 个结果")
                
                # 标记这是第几次调用的结果
                return f"[第{call_counter}次搜索] 搜索结果:\n\n{search_summary}"
            else:
                return f"[第{call_counter}次搜索] 未找到相关搜索结果"
        else:
            return f"[第{call_counter}次搜索] 搜索失败: HTTP {response.status_code}"
            
    except Exception as e:
        return f"[第{call_counter}次搜索] 搜索错误: {str(e)}"

# 设置组件
prompt = hub.pull("hwchase17/react")
llm = ChatOpenAI(model=MODEL, openai_api_key=API_KEY, openai_api_base=BASE_URL, temperature=0.1)
tools = [Tool(name="Search", func=tracked_search, description="搜索最新信息")]

agent = create_react_agent(llm, tools, prompt)
agent_executor = AgentExecutor(
    agent=agent, 
    tools=tools, 
    verbose=True, 
    handle_parsing_errors=True, 
    max_iterations=3
)

print("\n🎯 开始执行两次相同的调用")
print("=" * 80)

# 第一次调用
print("\n📋 第一次调用 agent_executor.invoke()")
print("-" * 50)
print("🕐 开始时间:", time.strftime('%H:%M:%S'))

try:
    result1 = agent_executor.invoke({"input": "当前Agent最新研究进展是什么?"})
    print(f"\n✅ 第一次调用完成")
    print(f"📤 返回结果长度: {len(result1.get('output', ''))}")
except Exception as e:
    print(f"❌ 第一次调用失败: {e}")
    result1 = None

print("\n" + "="*50)
time.sleep(2)  # 短暂间隔

# 第二次调用
print("\n📋 第二次调用 agent_executor.invoke()")
print("-" * 50)
print("🕐 开始时间:", time.strftime('%H:%M:%S'))

try:
    result2 = agent_executor.invoke({"input": "当前Agent最新研究进展是什么?"})
    print(f"\n✅ 第二次调用完成")
    print(f"📤 返回结果长度: {len(result2.get('output', ''))}")
except Exception as e:
    print(f"❌ 第二次调用失败: {e}")
    result2 = None

# 分析差异
print("\n🔍 差异分析")
print("=" * 80)

print(f"📊 统计信息:")
print(f"   - 总工具调用次数: {call_counter}")
print(f"   - 第一次调用是否成功: {'是' if result1 else '否'}")
print(f"   - 第二次调用是否成功: {'是' if result2 else '否'}")

if result1 and result2:
    output1 = result1.get('output', '')
    output2 = result2.get('output', '')
    
    print(f"\n📝 输出对比:")
    print(f"   - 第一次输出长度: {len(output1)} 字符")
    print(f"   - 第二次输出长度: {len(output2)} 字符")
    print(f"   - 输出是否相同: {'是' if output1 == output2 else '否'}")
    
    if output1 != output2:
        print(f"\n🔍 差异原因分析:")
        print("1. 🎲 模型的随机性 (temperature=0.1 仍有轻微随机性)")
        print("2. 🔄 搜索结果的时效性 (可能获取到不同的最新内容)")
        print("3. 🧠 模型推理路径的变化 (相同输入可能产生不同推理)")
        print("4. 🌐 网络状况影响 (API响应时间和内容可能不同)")
        
        # 显示前100个字符的对比
        print(f"\n📄 输出内容对比 (前100字符):")
        print(f"第一次: {output1[:100]}...")
        print(f"第二次: {output2[:100]}...")

print(f"\n💡 关键发现:")
print("1. 每次调用都是独立的，没有记忆前次调用")
print("2. Agent状态在每次调用后重置")
print("3. 搜索结果可能因时间差异而不同")
print("4. LLM的非确定性导致输出变化")
print("5. 网络延迟和API响应可能影响结果")

print(f"\n🔄 这与以下因素不同:")
print("- ❌ 不是因为Agent有记忆功能")
print("- ❌ 不是因为缓存机制")
print("- ❌ 不是因为状态保持")
print("- ✅ 而是因为每次都是全新的推理过程")
