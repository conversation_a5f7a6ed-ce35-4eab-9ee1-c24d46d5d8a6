"""
ReAct Agent 简化版本
使用DeepSeek模型的智能代理，不依赖外部搜索API
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 从环境变量获取配置信息
API_KEY = os.getenv("OPENAI_API_KEY", "sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56")
BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.ecovai.cn/v1")
MODEL = os.getenv("MODEL_NAME", "DeepSeek-V3-0324")

print("=" * 50)
print("🤖 ReAct Agent 简化版本")
print("=" * 50)
print(f"使用模型: {MODEL}")
print(f"API基础URL: {BASE_URL}")
print("=" * 50)

# 导入必要的库
from langchain_openai import ChatOpenAI
from langchain.agents.tools import Tool
from langchain.agents import create_react_agent, AgentExecutor
from langchain import hub

# 创建语言模型实例
llm = ChatOpenAI(
    model=MODEL,
    openai_api_key=API_KEY,
    openai_api_base=BASE_URL,
    temperature=0.1,
    max_tokens=2000,
)

# 创建一个简单的计算器工具
def calculator(expression):
    """简单的计算器工具"""
    try:
        # 安全的数学表达式计算
        allowed_chars = set('0123456789+-*/().')
        if not all(c in allowed_chars for c in expression.replace(' ', '')):
            return "错误：包含不允许的字符"
        
        result = eval(expression)
        return f"计算结果: {result}"
    except Exception as e:
        return f"计算错误: {str(e)}"

# 创建一个知识问答工具
def knowledge_qa(question):
    """基于模型知识的问答工具"""
    knowledge_prompt = f"""
    请基于你的知识回答以下问题，如果不确定请说明：
    问题：{question}
    
    请提供详细和准确的答案。
    """
    
    try:
        response = llm.invoke(knowledge_prompt)
        return response.content
    except Exception as e:
        return f"知识查询错误: {str(e)}"

# 准备工具列表
tools = [
    Tool(
        name="Calculator",
        func=calculator,
        description="用于进行数学计算。输入应该是数学表达式，如：2+3*4"
    ),
    Tool(
        name="Knowledge",
        func=knowledge_qa,
        description="用于回答基于知识的问题，特别是关于AI、机器学习、编程等技术问题"
    ),
]

print("✅ 工具创建成功")

# 获取ReAct提示模板
try:
    prompt = hub.pull("hwchase17/react")
    print("✅ ReAct提示模板获取成功")
except Exception as e:
    print(f"⚠️  获取提示模板失败: {e}")
    print("使用默认提示模板")
    
    # 使用简化的提示模板
    from langchain.prompts import PromptTemplate
    
    template = """Answer the following questions as best you can. You have access to the following tools:

{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {input}
Thought:{agent_scratchpad}"""

    prompt = PromptTemplate.from_template(template)

# 构建ReAct代理
agent = create_react_agent(llm, tools, prompt)

# 创建代理执行器
agent_executor = AgentExecutor(
    agent=agent, 
    tools=tools, 
    verbose=True,
    max_iterations=3,
    handle_parsing_errors=True,
)

print("✅ ReAct代理创建成功")

# 运行测试
print("\n" + "=" * 50)
print("🚀 开始测试ReAct代理")
print("=" * 50)

# 测试问题
test_questions = [
    "计算 15 * 8 + 32 的结果",
    "什么是ReAct框架？",
    "Agent的主要组成部分有哪些？",
]

for i, question in enumerate(test_questions, 1):
    print(f"\n📝 测试 {i}: {question}")
    print("-" * 30)
    
    try:
        result = agent_executor.invoke({"input": question})
        print(f"\n✅ 测试 {i} 完成")
        
    except Exception as e:
        print(f"❌ 测试 {i} 失败: {e}")

print("\n🎉 所有测试完成！")
