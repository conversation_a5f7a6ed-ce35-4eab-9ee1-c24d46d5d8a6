aiohttp==3.9.3
aiosignal==1.3.1
annotated-types==0.6.0
anthropic==0.25.7
anyio==4.2.0
asttokens==2.4.1
attrs==23.2.0
backoff==2.2.1
beautifulsoup4==4.12.3
bert-score==0.3.13
certifi==2024.2.2
cffi==1.16.0
chardet==5.2.0
charset-normalizer==3.3.2
click==8.1.7
cohere==4.47
colorama==0.4.6
comm==0.2.1
contourpy==1.2.0
cryptography==42.0.5
cycler==0.12.1
dataclasses-json==0.6.4
datasets==2.18.0
debugpy==1.8.1
decorator==5.1.1
defusedxml==0.7.1
dill==0.3.8
distro==1.9.0
docx==0.2.4
et-xmlfile==1.1.0
executing==2.0.1
fastapi==0.110.2
fastavro==1.9.4
filelock==3.13.1
fireworks-ai==0.14.0
fonttools==4.49.0
frozenlist==1.4.1
fsspec==2024.2.0
google_search_results==2.4.2
greenlet==3.0.3
h11==0.14.0
httpcore==1.0.3
httpx==0.26.0
httpx-sse==0.4.0
huggingface-hub==0.20.3
idna==3.6
importlib-metadata==6.11.0
ipykernel==6.29.3
ipython==8.22.2
jedi==0.19.1
Jinja2==3.1.2
joblib==1.3.2
jsonpatch==1.33
jsonpointer==2.4
jupyter_client==8.6.0
jupyter_core==5.7.1
kiwisolver==1.4.5
langchain==0.1.17
langchain-anthropic==0.1.11
langchain-community==0.0.36
langchain-core==0.1.52
langchain-experimental==0.0.58
langchain-openai==0.0.6
langchain-text-splitters==0.0.1
langchainhub==0.1.14
langgraph==0.0.30
langserve==0.1.1
langsmith==0.1.33
lxml==5.1.0
markdown-it-py==3.0.0
MarkupSafe==2.1.3
marshmallow==3.20.2
matplotlib==3.8.3
matplotlib-inline==0.1.6
mdurl==0.1.2
mpmath==1.3.0
multidict==6.0.5
multiprocess==0.70.16
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.2.1
nltk==3.8.1
numexpr==2.9.0
numpy==1.26.4
openai==1.21.0
openpyxl==3.1.2
orjson==3.9.15
packaging==23.2
pandas==2.2.1
parso==0.8.3
pdf2image==1.17.0
pdfminer==20191125
pdfminer.six==20231228
pillow==10.2.0
platformdirs==4.2.0
portalocker==2.8.2
prompt-toolkit==3.0.43
psutil==5.9.8
pure-eval==0.2.2
pyarrow==15.0.2
pyarrow-hotfix==0.6
pycparser==2.21
pycryptodome==3.20.0
pydantic==2.6.1
pydantic_core==2.16.2
Pygments==2.17.2
PyMuPDF==1.23.26
PyMuPDFb==1.23.22
pyparsing==3.1.2
pypdf==4.1.0
PyPDF2==3.0.1
python-dateutil==2.9.0.post0
python-docx==1.1.0
python-dotenv==1.0.1
pytz==2024.1
pywin32==306
PyYAML==6.0.1
pyzmq==25.1.2
regex==2023.12.25
requests==2.31.0
rich==13.7.1
rouge==1.0.1
safetensors==0.4.2
scikit-learn==1.4.1.post1
scipy==1.12.0
sentencepiece==0.2.0
six==1.16.0
sniffio==1.3.0
soupsieve==2.5
SQLAlchemy==2.0.27
sse-starlette==1.8.2
stack-data==0.6.3
starlette==0.37.2
sympy==1.12
tenacity==8.2.3
termcolor==2.4.0
threadpoolctl==3.3.0
tiktoken==0.6.0
tokenizers==0.15.2
torch==2.2.0
torchaudio==2.2.1+cu118
torchdata==0.7.1
torchtext==0.16.2
torchvision==0.17.1+cu118
tornado==6.4
tqdm==4.66.2
traitlets==5.14.1
transformers==4.38.1
types-requests==2.31.0.20240125
typing-inspect==0.9.0
typing_extensions==4.9.0
tzdata==2024.1
urllib3==2.2.0
uvicorn==0.29.0
wcwidth==0.2.13
wikipedia==1.4.0
xxhash==3.4.1
yarl==1.9.4
zipp==3.17.0
