# ReAct Agent 使用说明

## 项目概述

这个项目演示了如何使用LangChain框架创建一个ReAct（Reasoning and Acting）智能代理，该代理使用DeepSeek模型进行推理和行动。

## 文件说明

### 1. `ReAct.py` - 原始版本（已修改）
- 原始的ReAct代理实现
- 已修改为使用DeepSeek模型
- 依赖SerpAPI进行搜索

### 2. `ReAct_improved.py` - 改进版本
- 添加了完整的错误处理
- 更好的配置管理
- 详细的日志输出
- 支持SerpAPI搜索（可选）

### 3. `ReAct_simple.py` - 简化版本（推荐）
- 不依赖外部API
- 包含计算器和知识问答工具
- 更容易运行和测试

## 配置说明

### 环境变量配置

在项目根目录的 `.env` 文件中配置以下变量：

```env
# DeepSeek API 配置
OPENAI_API_KEY=sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56
OPENAI_BASE_URL=https://api.ecovai.cn/v1
MODEL_NAME=DeepSeek-V3-0324

# SerpAPI配置（可选，仅用于搜索功能）
# SERPAPI_API_KEY=your_serpapi_key_here
```

### 模型配置详解

- **API_KEY**: DeepSeek API密钥
- **BASE_URL**: API基础URL，指向DeepSeek服务
- **MODEL**: 使用的模型名称，这里是DeepSeek-V3-0324

## ReAct框架详解

### 什么是ReAct？

ReAct（Reasoning and Acting）是一种将推理和行动相结合的AI框架：

1. **Reasoning（推理）**: 模型分析问题，制定解决策略
2. **Acting（行动）**: 模型执行具体的工具调用或操作
3. **Observation（观察）**: 模型观察行动结果，调整后续策略

### ReAct工作流程

```
Question: 用户问题
↓
Thought: 分析问题，制定策略
↓
Action: 选择并执行工具
↓
Action Input: 提供工具输入
↓
Observation: 观察工具输出
↓
Thought: 基于观察结果继续推理
↓
Final Answer: 提供最终答案
```

### 代码核心组件

#### 1. 语言模型配置
```python
llm = ChatOpenAI(
    model=MODEL,
    openai_api_key=API_KEY,
    openai_api_base=BASE_URL,
    temperature=0.1,  # 低温度确保稳定输出
)
```

#### 2. 工具定义
```python
tools = [
    Tool(
        name="Calculator",
        func=calculator,
        description="用于进行数学计算"
    ),
    Tool(
        name="Knowledge",
        func=knowledge_qa,
        description="用于回答知识问题"
    ),
]
```

#### 3. 代理创建
```python
agent = create_react_agent(llm, tools, prompt)
agent_executor = AgentExecutor(
    agent=agent, 
    tools=tools, 
    verbose=True,
    max_iterations=3,
    handle_parsing_errors=True,
)
```

## 运行方式

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置环境变量
确保 `.env` 文件包含正确的API配置

### 3. 运行代码

#### 运行简化版本（推荐）
```bash
python 02-demo-agent/ReAct_simple.py
```

#### 运行改进版本
```bash
python 02-demo-agent/ReAct_improved.py
```

#### 运行原始版本
```bash
python 02-demo-agent/ReAct.py
```

## 预期输出

运行代码后，您将看到：

1. **配置信息**: 显示使用的模型和API配置
2. **工具创建**: 确认工具设置成功
3. **代理执行过程**: 详细的推理和行动步骤
4. **最终结果**: 每个测试问题的答案

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证BASE_URL是否可访问

2. **模型调用失败**
   - 确认模型名称正确
   - 检查API配额是否充足

3. **工具执行错误**
   - 查看详细错误信息
   - 确认工具输入格式正确

### 调试建议

1. 设置 `verbose=True` 查看详细执行过程
2. 检查 `.env` 文件配置
3. 测试API连接是否正常

## 扩展功能

您可以通过以下方式扩展代理功能：

1. **添加新工具**: 创建自定义工具函数
2. **修改提示模板**: 优化代理行为
3. **集成外部API**: 添加更多数据源
4. **优化参数**: 调整温度、最大迭代次数等

## 注意事项

1. **API费用**: 使用DeepSeek API会产生费用，请注意控制使用量
2. **网络要求**: 需要稳定的网络连接访问API
3. **安全性**: 不要在代码中硬编码API密钥，使用环境变量
4. **错误处理**: 生产环境中应添加更完善的错误处理机制
