aiohttp==3.9.3
aiosignal==1.3.1
annotated-types==0.6.0
anyio==4.3.0
attrs==23.2.0
beautifulsoup4==4.12.3
bs4==0.0.2
certifi==2024.2.2
charset-normalizer==3.3.2
click==8.1.7
colorama==0.4.6
dataclasses-json==0.6.4
Deprecated==1.2.14
dirtyjson==1.0.8
distro==1.9.0
faiss-cpu==1.8.0
frozenlist==1.4.1
fsspec==2024.3.1
greenlet==3.0.3
h11==0.14.0
httpcore==1.0.4
httpx==0.27.0
idna==3.6
joblib==1.3.2
llama-index==0.10.20
llama-index-agent-openai==0.1.6
llama-index-cli==0.1.10
llama-index-core==0.10.20.post2
llama-index-embeddings-openai==0.1.7
llama-index-indices-managed-llama-cloud==0.1.4
llama-index-legacy==0.9.48
llama-index-llms-openai==0.1.12
llama-index-multi-modal-llms-openai==0.1.4
llama-index-program-openai==0.1.4
llama-index-question-gen-openai==0.1.3
llama-index-readers-file==0.1.11
llama-index-readers-llama-parse==0.1.3
llama-index-vector-stores-faiss==0.1.2
llama-parse==0.3.9
llamaindex-py-client==0.1.13
marshmallow==3.21.1
multidict==6.0.5
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.2.1
nltk==3.8.1
numpy==1.26.4
openai==1.14.2
packaging==24.0
pandas==2.2.1
pillow==10.2.0
pydantic==2.6.4
pydantic_core==2.16.3
PyMuPDF==1.23.26
PyMuPDFb==1.23.22
pypdf==4.1.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2024.1
PyYAML==6.0.1
regex==2023.12.25
requests==2.31.0
six==1.16.0
sniffio==1.3.1
soupsieve==2.5
SQLAlchemy==2.0.28
striprtf==0.0.26
tenacity==8.2.3
tiktoken==0.6.0
tqdm==4.66.2
typing-inspect==0.9.0
typing_extensions==4.10.0
tzdata==2024.1
urllib3==2.2.1
wrapt==1.16.0
yarl==1.9.4
