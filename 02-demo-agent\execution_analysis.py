"""
ReAct程序执行流程详细分析
展示每一步的具体执行过程和数据流转
"""

import os
from dotenv import load_dotenv
load_dotenv()

print("=" * 80)
print("🔍 ReAct程序执行流程详细分析")
print("=" * 80)

# 第一步：环境配置和模块导入
print("\n📋 第一步：环境配置和模块导入")
print("-" * 50)

API_KEY = os.getenv("OPENAI_API_KEY", "sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56")
BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.ecovai.cn/v1")
MODEL = os.getenv("MODEL_NAME", "DeepSeek-V3-0324")

print(f"✅ 环境变量加载完成")
print(f"   - API_KEY: {API_KEY[:10]}...{API_KEY[-4:]}")
print(f"   - BASE_URL: {BASE_URL}")
print(f"   - MODEL: {MODEL}")

# 第二步：获取提示模板
print("\n📋 第二步：获取ReAct提示模板")
print("-" * 50)

from langchain import hub
prompt = hub.pull("hwchase17/react")

print(f"✅ 提示模板获取成功")
print(f"   - 模板变量: {prompt.input_variables}")
print(f"   - 模板类型: {type(prompt)}")

# 显示模板的详细结构
print("\n🔍 模板结构分析:")
template_parts = prompt.template.split('\n')
for i, part in enumerate(template_parts[:10], 1):  # 只显示前10行
    if '{' in part and '}' in part:
        print(f"   {i:2d}. [变量] {part}")
    elif part.strip() and not part.startswith(' '):
        print(f"   {i:2d}. [指令] {part}")
    else:
        print(f"   {i:2d}. [文本] {part}")

# 第三步：创建语言模型
print("\n📋 第三步：创建语言模型实例")
print("-" * 50)

from langchain_openai import ChatOpenAI

llm = ChatOpenAI(
    model=MODEL,
    openai_api_key=API_KEY,
    openai_api_base=BASE_URL,
    temperature=0.1,
)

print(f"✅ 语言模型创建成功")
print(f"   - 模型类型: {type(llm)}")
print(f"   - 模型名称: {llm.model_name}")
print(f"   - 温度设置: {llm.temperature}")

# 第四步：定义工具
print("\n📋 第四步：定义工具函数")
print("-" * 50)

def mock_search(query):
    """模拟搜索工具，用于演示"""
    return f"模拟搜索结果：关于'{query}'的信息..."

from langchain_core.tools import Tool

tools = [
    Tool(
        name="Search",
        func=mock_search,
        description="当大模型没有相关知识时，用于搜索知识"
    ),
]

print(f"✅ 工具定义完成")
print(f"   - 工具数量: {len(tools)}")
print(f"   - 工具名称: {[tool.name for tool in tools]}")
print(f"   - 工具描述: {tools[0].description}")

# 第五步：创建ReAct代理
print("\n📋 第五步：创建ReAct代理")
print("-" * 50)

from langchain.agents import create_react_agent

agent = create_react_agent(llm, tools, prompt)

print(f"✅ ReAct代理创建成功")
print(f"   - 代理类型: {type(agent)}")
print(f"   - 包含组件: LLM + Tools + Prompt")

# 第六步：创建代理执行器
print("\n📋 第六步：创建代理执行器")
print("-" * 50)

from langchain.agents import AgentExecutor

agent_executor = AgentExecutor(
    agent=agent,
    tools=tools,
    verbose=True,
    handle_parsing_errors=True,
    max_iterations=3,
)

print(f"✅ 代理执行器创建成功")
print(f"   - 执行器类型: {type(agent_executor)}")
print(f"   - 最大迭代次数: {agent_executor.max_iterations}")
print(f"   - 错误处理: {agent_executor.handle_parsing_errors}")

# 第七步：模拟执行过程
print("\n📋 第七步：执行过程模拟分析")
print("-" * 50)

user_input = "当前Agent最新研究进展是什么?"
print(f"🎯 用户输入: {user_input}")

# 模拟提示模板的填充过程
print("\n🔄 提示模板填充过程:")
print("1. 原始模板变量:")
for var in prompt.input_variables:
    print(f"   - {var}: 待填充")

print("\n2. 填充后的提示模板预览:")
filled_template = prompt.template.format(
    tools="Search: 当大模型没有相关知识时，用于搜索知识",
    tool_names="Search",
    input=user_input,
    agent_scratchpad=""
)

# 显示填充后的模板（前几行）
lines = filled_template.split('\n')
for i, line in enumerate(lines[:15], 1):
    if '{' not in line:  # 已填充的行
        print(f"   {i:2d}. {line}")
    else:  # 仍有变量的行
        print(f"   {i:2d}. [待填充] {line}")

print("\n🔄 执行流程预期步骤:")
print("1. 模型接收填充后的提示")
print("2. 模型生成 Thought（思考）")
print("3. 模型决定 Action（行动）")
print("4. 执行器调用相应工具")
print("5. 工具返回 Observation（观察结果）")
print("6. 模型基于观察结果继续推理")
print("7. 重复步骤2-6，直到得出 Final Answer")

print("\n" + "=" * 80)
print("🎉 程序执行流程分析完成！")
print("=" * 80)
