import os
from dotenv import load_dotenv
load_dotenv()

# 从环境变量获取配置信息
API_KEY = os.getenv("OPENAI_API_KEY", "sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56")
BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.ecovai.cn/v1")
MODEL = os.getenv("MODEL_NAME", "DeepSeek-V3-0324")

# Search1API配置
SEARCH1API_KEY = os.getenv("SEARCH1API_KEY", "B8676CDF-DB51-4D78-8AC2-BE11ED21FD9A")
SEARCH1API_URL = os.getenv("SEARCH1API_URL", "https://api.search1api.com/search")

print(f"使用模型: {MODEL}")
print(f"API基础URL: {BASE_URL}")
print(f"搜索API: Search1API")

# 导入LangChain Hub
from langchain import hub
# 从hub中获取React的Prompt
prompt = hub.pull("hwchase17/react")
print(prompt)

# 导入ChatOpenAI（使用ChatOpenAI而不是OpenAI以获得更好的性能）
from langchain_openai import ChatOpenAI
# 选择要使用的LLM，配置为使用DeepSeek模型
llm = ChatOpenAI(
    model=MODEL,
    openai_api_key=API_KEY,
    openai_api_base=BASE_URL,
    temperature=0.1,  # 设置较低的温度以获得更稳定的输出
)

# 导入工具相关模块
from langchain_core.tools import Tool
import requests
import json
from datetime import datetime

# 注意：Search1API配置已在文件开头从环境变量获取

def search1api_search(query):
    """使用Search1API进行搜索"""
    try:
        print(f"🔍 正在搜索: {query}")

        # 构建请求参数
        payload = {
            "query": query,
            "search_service": "google",
            "max_results": 5,
            "crawl_results": 0,
            "image": False,
            "include_sites": [],
            "exclude_sites": [],
            "language": "zh",  # 设置为中文
            "time_range": "year"
        }

        # 设置请求头
        headers = {
            "Authorization": f"Bearer {SEARCH1API_KEY}",
            "Content-Type": "application/json"
        }

        # 发送请求
        response = requests.post(SEARCH1API_URL, json=payload, headers=headers, timeout=10)

        if response.status_code == 200:
            data = response.json()
            results = data.get("results", [])

            if results:
                # 格式化搜索结果
                formatted_results = []
                for i, result in enumerate(results[:3], 1):  # 只取前3个结果
                    title = result.get("title", "无标题")
                    snippet = result.get("snippet", "无摘要")
                    link = result.get("link", "")

                    formatted_results.append(f"{i}. {title}\n   摘要: {snippet}\n   链接: {link}")

                search_summary = "\n\n".join(formatted_results)
                print(f"✅ 搜索完成，找到 {len(results)} 个结果")
                return f"搜索结果:\n\n{search_summary}"
            else:
                return "未找到相关搜索结果"
        else:
            print(f"❌ 搜索请求失败，状态码: {response.status_code}")
            return f"搜索失败: HTTP {response.status_code}"

    except requests.exceptions.Timeout:
        print("⏰ 搜索请求超时")
        return "搜索超时，请稍后重试"
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求错误: {e}")
        return f"网络错误: {str(e)}"
    except Exception as e:
        print(f"❌ 搜索过程中发生错误: {e}")
        return f"搜索错误: {str(e)}"

# 添加获取当前时间的工具
def get_current_time():
    """获取当前时间信息"""
    now = datetime.now()
    return f"当前时间: {now.strftime('%Y年%m月%d日')}, 当前年份: {now.year}"

# 准备工具列表
tools = [
    Tool(
        name="Search",
        func=search1api_search,
        description="当大模型没有相关知识时，用于搜索最新信息和知识。输入应该是搜索查询关键词。"
    ),
    Tool(
        name="GetCurrentTime",
        func=get_current_time,
        description="获取当前的日期和年份信息，用于确定什么是'最新'或'当前'的时间范围。"
    ),
]

# 导入create_react_agent功能
from langchain.agents import create_react_agent
# 构建ReAct代理
agent = create_react_agent(llm, tools, prompt)

# 导入AgentExecutor
from langchain.agents import AgentExecutor
# 创建代理执行器并传入代理和工具
agent_executor = AgentExecutor(
    agent=agent,
    tools=tools,
    verbose=True,
    handle_parsing_errors=True,  # 处理解析错误
    max_iterations=3,  # 限制最大迭代次数
)

# 调用代理执行器，传入输入数据
print("第一次运行的结果：")
agent_executor.invoke({"input": "当前Agent最新研究进展是什么?"})
print("第二次运行的结果：")
agent_executor.invoke({"input": "当前Agent最新研究进展是什么?"})