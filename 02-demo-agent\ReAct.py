import os
from dotenv import load_dotenv
load_dotenv()

# 从环境变量获取配置信息
API_KEY = os.getenv("OPENAI_API_KEY", "sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56")
BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.ecovai.cn/v1")
MODEL = os.getenv("MODEL_NAME", "DeepSeek-V3-0324")

print(f"使用模型: {MODEL}")
print(f"API基础URL: {BASE_URL}")

# 导入LangChain Hub
from langchain import hub
# 从hub中获取React的Prompt
prompt = hub.pull("hwchase17/react")
print(prompt)

# 导入ChatOpenAI（使用ChatOpenAI而不是OpenAI以获得更好的性能）
from langchain_openai import ChatOpenAI
# 选择要使用的LLM，配置为使用DeepSeek模型
llm = ChatOpenAI(
    model=MODEL,
    openai_api_key=API_KEY,
    openai_api_base=BASE_URL,
    temperature=0.1,  # 设置较低的温度以获得更稳定的输出
)

# 导入SerpAPIWrapper即工具包
from langchain_community.utilities import SerpAPIWrapper
from langchain.agents.tools import Tool
# 实例化SerpAPIWrapper
search = SerpAPIWrapper()
# 准备工具列表
tools = [
    Tool(
        name="Search",
        func=search.run,
        description="当大模型没有相关知识时，用于搜索知识"
    ),
]

# 导入create_react_agent功能
from langchain.agents import create_react_agent
# 构建ReAct代理
agent = create_react_agent(llm, tools, prompt)

# 导入AgentExecutor
from langchain.agents import AgentExecutor
# 创建代理执行器并传入代理和工具
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

# 调用代理执行器，传入输入数据
print("第一次运行的结果：")
agent_executor.invoke({"input": "当前Agent最新研究进展是什么?"})
print("第二次运行的结果：")
agent_executor.invoke({"input": "当前Agent最新研究进展是什么?"})